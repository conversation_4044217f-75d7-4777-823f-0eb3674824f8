##########################################################################################
#                        Append Compiler Options For Source Files
##########################################################################################

# syntax:
#   <your pattern>: <compiler options>
# For get pattern syntax, please refer to: https://www.npmjs.com/package/micromatch
#
# examples:
#   'main.cpp':           --cpp11 -Og ...
#   'src/*.c':            -gnu -O2 ...
#   'src/lib/**/*.cpp':   --cpp11 -Os ...
#   '!Application/*.c':   -O0
#   '**/*.c':             -O2 -gnu ...

version: "2.0"
options:
    GD32_Xifeng:
        files: {}
        virtualPathFiles:
            <virtual_root>/Application/User/Core/adc.c: ""
            <virtual_root>/Application/User/Core/dac.c: ""
            <virtual_root>/Application/User/Core/tim.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_adc.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_adc_ex.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_ll_adc.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_dac.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_dac_ex.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_tim.c: ""
            <virtual_root>/Drivers/STM32F4xx_HAL_Driver/stm32f4xx_hal_tim_ex.c: ""
