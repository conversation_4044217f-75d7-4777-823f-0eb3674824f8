
/**
 * @file    config_manager.c
 * @brief   系统配置管理模块实现
 * @details 提供参数存储到Flash的持久化功能，支持配置验证和CRC校验
 * <AUTHOR>
 * @date    2024
 * @version 2.0
 */

#include "config_manager.h"
#include "stddef.h"  // NULL定义
#include "string.h"  // 字符串函数
#include "gd25qxx.h" // Flash驱动接口

/* ================================ 私有变量定义 ================================ */
static cfg_params_t g_cfg_params = {0};    // 当前配置参数缓存
static uint8_t g_cfg_initialized = 0;      // 初始化标志

/**
 * @brief 默认配置参数
 * @note 系统首次启动或配置损坏时使用
 */
static const cfg_params_t g_default_config = {
    .magic = CFG_MAGIC_NUMBER,
    .version = CFG_VERSION_CURRENT,
    .ratio = 1.0f,     // 默认比例
    .limit = 100.0f,   // 默认限值
    .cycle = CYCLE_5S, // 默认采样周期5秒
    .crc32 = 0         // CRC32将在初始化时计算
};

/**
 * @brief CRC32查找表 - 优化版本
 * @note 使用查找表提高CRC计算效率
 */
static const uint32_t g_crc32_table[16] = {
    0x00000000, 0x1DB71064, 0x3B6E20C8, 0x26D930AC,
    0x76DC4190, 0x6B6B51F4, 0x4DB26158, 0x5005713C,
    0xEDB88320, 0xF00F9344, 0xD6D6A3E8, 0xCB61B38C,
    0x9B64C2B0, 0x86D3D2D4, 0xA00AE278, 0xBDBDF21C
};

/* ================================ 私有函数实现 ================================ */
/**
 * @brief 计算CRC32校验值
 * @param params 配置结构体指针
 * @return uint32_t CRC32校验值
 * @note 使用查找表算法提高计算效率
 */
uint32_t cfg_calculate_crc32(const cfg_params_t *params)
{
    if (params == NULL) {
        return 0;
    }

    uint32_t crc = 0xFFFFFFFF;                                  // 初始化CRC值
    const uint8_t *data = (const uint8_t *)params;             // 数据指针
    uint32_t len = sizeof(cfg_params_t) - sizeof(uint32_t);    // 排除CRC32字段

    // 使用查找表计算CRC
    for (uint32_t i = 0; i < len; i++) {
        crc = g_crc32_table[(crc ^ data[i]) & 0x0F] ^ (crc >> 4);
        crc = g_crc32_table[(crc ^ (data[i] >> 4)) & 0x0F] ^ (crc >> 4);
    }

    return crc ^ 0xFFFFFFFF; // 返回最终CRC值
}

/* ================================ 参数验证函数 ================================ */
/**
 * @brief 验证比例参数
 * @param ratio 比例值
 * @return cfg_status_t 验证结果
 */
cfg_status_t cfg_validate_ratio(float ratio)
{
    return (ratio >= CFG_RATIO_MIN && ratio <= CFG_RATIO_MAX) ?
           CFG_STATUS_OK : CFG_STATUS_INVALID;
}

/**
 * @brief 验证限值参数
 * @param limit 限值
 * @return cfg_status_t 验证结果
 */
cfg_status_t cfg_validate_limit(float limit)
{
    return (limit >= CFG_LIMIT_MIN && limit <= CFG_LIMIT_MAX) ?
           CFG_STATUS_OK : CFG_STATUS_INVALID;
}

/**
 * @brief 验证采样周期参数
 * @param cycle 周期值
 * @return cfg_status_t 验证结果
 */
cfg_status_t cfg_validate_sampling_cycle(sampling_cycle_t cycle)
{
    return (cycle == CYCLE_5S || cycle == CYCLE_10S || cycle == CYCLE_15S) ?
           CFG_STATUS_OK : CFG_STATUS_INVALID;
}

/* ================================ 公共接口函数实现 ================================ */
/**
 * @brief 初始化配置管理系统
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_init(void)
{
    if (g_cfg_initialized) {
        return CFG_STATUS_OK;
    }

    // 尝试从Flash加载配置
    cfg_status_t status = cfg_load_from_flash();
    if (status != CFG_STATUS_OK) {
        // 加载失败，使用默认配置
        g_cfg_params = g_default_config;
        g_cfg_params.crc32 = cfg_calculate_crc32(&g_cfg_params);
    }

    g_cfg_initialized = 1;
    return CFG_STATUS_OK;
}

/**
 * @brief 获取当前配置参数
 * @param params 配置参数结构体指针
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_get_params(cfg_params_t *params)
{
    if (params == NULL) {
        return CFG_STATUS_ERROR;
    }
    if (!g_cfg_initialized) {
        return CFG_STATUS_ERROR;
    }

    *params = g_cfg_params;
    return CFG_STATUS_OK;
}

/**
 * @brief 设置配置参数
 * @param params 配置参数结构体指针
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_set_params(const cfg_params_t *params)
{
    if (params == NULL) {
        return CFG_STATUS_ERROR;
    }
    if (!g_cfg_initialized) {
        return CFG_STATUS_ERROR;
    }

    // 验证所有参数
    if (cfg_validate_ratio(params->ratio) != CFG_STATUS_OK) {
        return CFG_STATUS_INVALID;
    }
    if (cfg_validate_limit(params->limit) != CFG_STATUS_OK) {
        return CFG_STATUS_INVALID;
    }
    if (cfg_validate_sampling_cycle(params->cycle) != CFG_STATUS_OK) {
        return CFG_STATUS_INVALID;
    }

    // 更新配置参数
    g_cfg_params.ratio = params->ratio;
    g_cfg_params.limit = params->limit;
    g_cfg_params.cycle = params->cycle;
    g_cfg_params.crc32 = cfg_calculate_crc32(&g_cfg_params);

    return CFG_STATUS_OK;
}

/**
 * @brief 重置为默认配置
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_reset_to_default(void)
{
    g_cfg_params = g_default_config;
    g_cfg_params.crc32 = cfg_calculate_crc32(&g_cfg_params);
    g_cfg_initialized = 1;
    return CFG_STATUS_OK;
}

/**
 * @brief 保存配置到Flash
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_save_to_flash(void)
{
    if (!g_cfg_initialized) {
        return CFG_STATUS_ERROR;
    }

    // 更新CRC32校验值
    g_cfg_params.crc32 = cfg_calculate_crc32(&g_cfg_params);

    // 擦除Flash扇区(4KB对齐扇区)
    spi_flash_sector_erase(CFG_FLASH_ADDR);

    // 写入配置数据到Flash
    spi_flash_buffer_write((uint8_t *)&g_cfg_params, CFG_FLASH_ADDR, sizeof(cfg_params_t));

    return CFG_STATUS_OK;
}

/**
 * @brief 从Flash加载配置
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_load_from_flash(void)
{
    cfg_params_t temp_config;

    // 从Flash读取配置数据
    spi_flash_buffer_read((uint8_t *)&temp_config, CFG_FLASH_ADDR, sizeof(cfg_params_t));

    // 验证魔数
    if (temp_config.magic != CFG_MAGIC_NUMBER) {
        return CFG_STATUS_ERROR;
    }

    // 验证版本号
    if (temp_config.version != CFG_VERSION_CURRENT) {
        return CFG_STATUS_ERROR;
    }

    // 验证CRC32校验
    uint32_t calculated_crc = cfg_calculate_crc32(&temp_config);
    if (calculated_crc != temp_config.crc32) {
        return CFG_STATUS_CRC_ERROR;
    }

    // 验证参数范围
    if (cfg_validate_ratio(temp_config.ratio) != CFG_STATUS_OK ||
        cfg_validate_limit(temp_config.limit) != CFG_STATUS_OK ||
        cfg_validate_sampling_cycle(temp_config.cycle) != CFG_STATUS_OK) {
        return CFG_STATUS_INVALID;
    }

    // 配置有效，完全复制
    g_cfg_params = temp_config;
    return CFG_STATUS_OK;
}

/* ================================ 采样周期专用函数 ================================ */
/**
 * @brief 设置采样周期
 * @param cycle 周期值
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_set_sampling_cycle(sampling_cycle_t cycle)
{
    if (!g_cfg_initialized) {
        return CFG_STATUS_ERROR;
    }

    if (cfg_validate_sampling_cycle(cycle) != CFG_STATUS_OK) {
        return CFG_STATUS_INVALID;
    }

    g_cfg_params.cycle = cycle;
    g_cfg_params.crc32 = cfg_calculate_crc32(&g_cfg_params);

    return CFG_STATUS_OK;
}

/**
 * @brief 获取采样周期
 * @return sampling_cycle_t 当前周期
 */
sampling_cycle_t cfg_get_sampling_cycle(void)
{
    if (!g_cfg_initialized) {
        return CYCLE_5S;
    }
    return g_cfg_params.cycle;
}
