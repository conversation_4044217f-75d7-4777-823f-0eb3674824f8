# STM32F4 嵌入式系统功能演示

## 概述

这是一个为 STM32F4 嵌入式系统创建的综合演示程序，包含了系统自检、RTC时间管理和基本输出功能。

## 功能特性

### 1. 系统自检功能
- **指令**: `test`
- **功能**: 检测Flash存储器、TF卡状态，显示系统信息
- **输出**: Flash ID、TF卡容量、RTC时间等

### 2. RTC时间管理
- **时间设置**: `RTC Config` - 支持多种时间格式输入
- **时间查询**: `RTC now` - 显示当前时间
- **格式支持**: 标准格式、中文格式、混合格式

### 3. UART 串口输出
- 通过 UART1 (波特率: 460800) 输出系统信息
- 显示系统信息、编译日期和时间
- 使用 `printf` 函数（已重定向到 UART）

### 4. OLED 显示
- 支持两种 OLED 显示方式：
  - 自定义 OLED 库显示
  - U8G2 图形库显示
- 128x32 像素 SSD1306 OLED 显示屏
- I2C 接口通信

## 文件结构

```
APP/
├── system_check.h/.c  # 系统自检模块
├── rtc_app.h/.c      # RTC时间管理模块
├── usart_app.h/.c    # 串口通信处理
├── hello_world.h/.c  # Hello World演示
└── mydefine.h        # 统一配置文件
Core/Src/
└── main.c            # 主程序
```

## 主要功能模块

### 系统自检模块 (`system_check.c`)
- `system_self_check()`: 执行完整系统自检
- `check_flash_status()`: Flash存储器状态检测
- `check_tf_card_status()`: TF卡状态检测
- `print_system_info()`: 格式化输出系统信息

### RTC时间管理 (`rtc_app.c`)
- `rtc_set_time_from_string()`: 从字符串设置RTC时间
- `rtc_print_current_time()`: 打印当前时间
- `parse_time_string()`: 解析多种时间格式

### 串口命令处理 (`usart_app.c`)
- `parse_uart_command()`: 解析串口命令
- `handle_rtc_config_command()`: 处理RTC配置命令
- `handle_interactive_input()`: 处理交互式输入

### Hello World演示 (`hello_world.c`)
- `hello_world_print_uart()`: 串口输出演示
- `hello_world_display_oled()`: OLED显示演示
- `hello_world_run_all()`: 综合演示

## 使用方法

### 1. 编译和烧录
1. **编译项目**
   - 使用 MDK-ARM 或其他支持的 IDE 编译项目
   - 确保所有依赖库已正确配置

2. **烧录程序**
   - 将编译后的程序烧录到 STM32F4 微控制器

### 2. 串口命令使用
连接串口调试工具（波特率 460800），支持以下命令：

#### 系统自检
```
test
```
输出示例：
```
flash..........ok
TF card.......ok
flash ID 0xC84017
TF card memory: 8192 KB
RTC：2025-01-01 01:00:50
```

#### RTC时间设置
```
RTC Config
```
然后输入时间（支持多种格式）：
- `2025-01-01 12:00:30`
- `2025年01月01日12:00:30`
- `2025-01-01 01-30-10`

#### RTC时间查询
```
RTC now
```
输出示例：
```
Current Time: 2025-01-01 12:00:30
```

## 硬件要求

- STM32F4 系列微控制器
- SPI Flash存储器（如GD25Q64）
- TF卡（SD卡）及卡槽
- 128x32 SSD1306 OLED 显示屏（I2C 接口）
- UART 串口连接（用于调试输出）
- RTC时钟源

## 设计特点

### 低耦合设计
- 各模块功能独立，接口清晰
- 系统自检模块与其他模块松耦合
- 便于扩展和维护

### 中文友好
- 支持中文时间格式输入
- 注释使用中文，便于理解
- 错误提示友好

### 性能优化
- 最少代码行数实现功能
- 避免重复代码
- 高效的字符串处理

### 统一配置管理
- 所有变量通过mydefine.h统一管理
- 避免重复定义
- 便于维护

## 技术细节

- **UART 配置**: 460800 波特率，8 数据位，1 停止位，无校验
- **I2C 配置**: 用于 OLED 通信
- **SPI 配置**: 用于 Flash 存储器通信
- **SDIO 配置**: 用于 TF 卡通信
- **RTC 配置**: 24小时格式，支持多种时间输入格式
- **显示库**: 支持自定义 OLED 库和 U8G2 图形库

## 扩展建议

1. 添加更多硬件检测项目（温度传感器、电压监测等）
2. 实现系统状态监控和报警功能
3. 添加数据记录和分析功能
4. 实现远程监控和控制
5. 集成到更大的物联网系统中

## 注意事项

- 确保所有硬件正确连接
- Flash和TF卡需要正确初始化
- RTC时钟源需要配置正确
- 串口调试需要正确的波特率设置
- 系统自检会在每次"test"命令时执行

## 故障排除

1. **Flash检测失败**: 检查SPI连接和Flash芯片
2. **TF卡检测失败**: 检查SDIO连接和卡片格式
3. **RTC时间设置失败**: 检查时间格式和RTC配置
4. **串口无输出**: 检查波特率和连接线

## 更新日志

- **v1.1**: 添加系统自检和RTC时间管理功能
- **v1.0**: 初始Hello World演示版本
