/**
 ******************************************************************************
 * @file           : hello_world_test.c
 * @brief          : Hello World test functions
 ******************************************************************************
 * @attention
 *
 * This file contains test functions for the Hello World module
 * to verify functionality without affecting the main system operation.
 *
 ******************************************************************************
 */

#include "hello_world.h"
#include "mydefine.h"

/* 外部变量声明 */
extern u8g2_t u8g2;

/**
 * @brief  测试 UART 输出功能
 * @retval None
 */
void test_uart_output(void)
{
    printf("\r\n=== UART Output Test ===\r\n");
    printf("Testing printf functionality...\r\n");
    printf("Numbers: %d, %f\r\n", 123, 3.14159);
    printf("Hex: 0x%X\r\n", 0xABCD);
    printf("String: %s\r\n", "Hello from STM32!");
    printf("UART test completed!\r\n");
}

/**
 * @brief  测试 OLED 显示功能
 * @retval None
 */
void test_oled_display(void)
{
    printf("Testing OLED display...\r\n");
    
    // 测试自定义 OLED 库
    OLED_Clear();
    OLED_ShowStr(0, 0, "OLED Test", 16);
    OLED_ShowStr(0, 2, "Custom Lib", 8);
    
    HAL_Delay(2000);
    
    // 测试 U8G2 库
    u8g2_ClearBuffer(&u8g2);
    u8g2_SetFont(&u8g2, u8g2_font_ncenB08_tr);
    u8g2_DrawStr(&u8g2, 0, 10, "U8G2 Test");
    u8g2_DrawStr(&u8g2, 0, 25, "Graphics Lib");
    u8g2_SendBuffer(&u8g2);
    
    printf("OLED test completed!\r\n");
}

/**
 * @brief  测试数字显示功能
 * @retval None
 */
void test_number_display(void)
{
    printf("Testing number display...\r\n");
    
    // 显示数字
    OLED_Clear();
    OLED_ShowStr(0, 0, "Numbers:", 8);
    OLED_ShowNum(0, 1, 12345, 5, 8);
    OLED_ShowFloat(0, 2, 3.14159, 3, 8);
    
    HAL_Delay(2000);
    
    // 使用 U8G2 显示数字
    u8g2_ClearBuffer(&u8g2);
    u8g2_SetFont(&u8g2, u8g2_font_ncenB08_tr);
    u8g2_DrawStr(&u8g2, 0, 10, "Count: 123");
    u8g2_DrawStr(&u8g2, 0, 25, "Value: 3.14");
    u8g2_SendBuffer(&u8g2);
    
    printf("Number display test completed!\r\n");
}

/**
 * @brief  运行所有测试
 * @retval None
 */
void run_all_tests(void)
{
    printf("\r\n========================================\r\n");
    printf("Starting Hello World Test Suite\r\n");
    printf("========================================\r\n");
    
    // 测试 UART 输出
    test_uart_output();
    HAL_Delay(1000);
    
    // 测试 OLED 显示
    test_oled_display();
    HAL_Delay(1000);
    
    // 测试数字显示
    test_number_display();
    HAL_Delay(1000);
    
    // 运行原始 Hello World 演示
    hello_world_run_all();
    
    printf("\r\n========================================\r\n");
    printf("All tests completed successfully!\r\n");
    printf("========================================\r\n");
}
