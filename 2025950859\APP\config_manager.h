/**
 * @file    config_manager.h
 * @brief   系统配置管理模块头文件
 * @details 提供参数存储到Flash的持久化接口，支持配置验证和CRC校验
 * <AUTHOR>
 * @date    2024
 * @version 2.0
 */

#ifndef __CONFIG_MANAGER_H__
#define __CONFIG_MANAGER_H__

#include "stdint.h"
#include "sampling_control.h" // 采样控制模块

/* ================================ 配置常量定义 ================================ */
#define CFG_FLASH_ADDR      0x1F0000    // Flash存储地址
#define CFG_MAGIC_NUMBER    0x43464721  // 配置魔数标识
#define CFG_VERSION_CURRENT 0x02        // 配置版本号

/* 参数范围定义 */
#define CFG_RATIO_MIN       0.0f        // 比例参数最小值
#define CFG_RATIO_MAX       100.0f      // 比例参数最大值
#define CFG_LIMIT_MIN       0.0f        // 限值参数最小值
#define CFG_LIMIT_MAX       500.0f      // 限值参数最大值

/* ================================ 数据结构定义 ================================ */
/**
 * @brief 配置参数结构体
 * @note  所有配置参数的集合，包含CRC校验
 */
typedef struct {
    uint32_t magic;              // 魔数标识，用于验证数据有效性
    uint8_t version;             // 版本号，用于兼容性检查
    float ratio;                 // 比例参数，范围0-100
    float limit;                 // 限值参数，范围0-500
    sampling_cycle_t cycle;      // 采样周期参数(5s/10s/15s)
    uint32_t crc32;              // CRC32校验值，确保数据完整性
} cfg_params_t;

/**
 * @brief 配置操作状态枚举
 */
typedef enum {
    CFG_STATUS_OK = 0,           // 操作成功
    CFG_STATUS_ERROR,            // 操作失败
    CFG_STATUS_INVALID,          // 参数无效
    CFG_STATUS_FLASH_ERROR,      // Flash操作错误
    CFG_STATUS_CRC_ERROR         // CRC校验错误
} cfg_status_t;

/* ================================ 公共接口函数 ================================ */
/**
 * @brief 初始化配置管理系统
 * @return cfg_status_t 操作状态
 * @note 系统启动时调用，从Flash加载配置或使用默认值
 */
cfg_status_t cfg_init(void);

/**
 * @brief 获取当前配置参数
 * @param params 配置参数结构体指针
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_get_params(cfg_params_t *params);

/**
 * @brief 设置配置参数
 * @param params 配置参数结构体指针
 * @return cfg_status_t 操作状态
 * @note 会自动进行参数验证和CRC计算
 */
cfg_status_t cfg_set_params(const cfg_params_t *params);

/**
 * @brief 保存配置到Flash
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_save_to_flash(void);

/**
 * @brief 从Flash加载配置
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_load_from_flash(void);

/**
 * @brief 重置为默认配置
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_reset_to_default(void);

/* ================================ 参数验证函数 ================================ */
/**
 * @brief 验证比例参数
 * @param ratio 比例值
 * @return cfg_status_t 验证结果
 */
cfg_status_t cfg_validate_ratio(float ratio);

/**
 * @brief 验证限值参数
 * @param limit 限值
 * @return cfg_status_t 验证结果
 */
cfg_status_t cfg_validate_limit(float limit);

/**
 * @brief 验证采样周期参数
 * @param cycle 周期值
 * @return cfg_status_t 验证结果
 */
cfg_status_t cfg_validate_sampling_cycle(sampling_cycle_t cycle);

/* ================================ 采样周期专用函数 ================================ */
/**
 * @brief 设置采样周期
 * @param cycle 周期值
 * @return cfg_status_t 操作状态
 */
cfg_status_t cfg_set_sampling_cycle(sampling_cycle_t cycle);

/**
 * @brief 获取采样周期
 * @return sampling_cycle_t 当前周期
 */
sampling_cycle_t cfg_get_sampling_cycle(void);

/* ================================ 工具函数 ================================ */
/**
 * @brief 计算CRC32校验值
 * @param params 配置结构体指针
 * @return uint32_t 校验值
 */
uint32_t cfg_calculate_crc32(const cfg_params_t *params);

/* ================================ 兼容性接口 ================================ */
// 为了保持向后兼容，提供旧接口的宏定义
#define config_params_t         cfg_params_t
#define config_status_t         cfg_status_t
#define CONFIG_OK               CFG_STATUS_OK
#define CONFIG_ERROR            CFG_STATUS_ERROR
#define CONFIG_INVALID          CFG_STATUS_INVALID
#define CONFIG_FLASH_ERROR      CFG_STATUS_FLASH_ERROR
#define CONFIG_CRC_ERROR        CFG_STATUS_CRC_ERROR

#define config_init             cfg_init
#define config_get_params       cfg_get_params
#define config_set_params       cfg_set_params
#define config_save_to_flash    cfg_save_to_flash
#define config_load_from_flash  cfg_load_from_flash
#define config_reset_to_default cfg_reset_to_default
#define config_validate_ratio   cfg_validate_ratio
#define config_validate_limit   cfg_validate_limit
#define config_validate_sampling_cycle cfg_validate_sampling_cycle
#define config_set_sampling_cycle cfg_set_sampling_cycle
#define config_get_sampling_cycle cfg_get_sampling_cycle
#define config_calculate_crc32  cfg_calculate_crc32

#endif /* __CONFIG_MANAGER_H__ */
