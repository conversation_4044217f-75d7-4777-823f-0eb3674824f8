# 系统自检和RTC时间设置功能实现说明

## 功能概述

本次实现了两个主要功能：
1. 系统自检功能（"test"指令）
2. RTC时间设置功能（"RTC Config"和"RTC now"指令）

## 1. 系统自检功能

### 指令格式
```
test
```

### 功能描述
通过串口输入"test"指令，系统会进行全面自检，包括：
- Flash存储器检测（通过读取ID判断）
- TF卡检测（检查是否存在）
- 显示Flash ID
- 显示TF卡容量（如果存在）
- 显示当前RTC时间

### 输出样例

#### 正常情况（Flash和TF卡都正常）
```
flash..........ok
TF card.......ok
flash ID 0xC84017
TF card memory: 8192 KB
RTC：2025-01-01 01:00:50
```

#### TF卡异常情况
```
flash..........ok
TF card.......error
flash ID 0xC84017
can not find TF card
RTC：2025-01-01 01:00:50
```

### 实现细节
- Flash检测：通过`spi_flash_read_id()`读取Flash ID，判断是否为有效ID（非0x000000或0xFFFFFF）
- TF卡检测：使用`disk_initialize(0)`初始化SD卡，根据返回状态判断
- 容量计算：TF卡容量以KB为单位显示
- 输出格式：使用点号对齐，符合要求的格式

## 2. RTC时间设置功能

### 2.1 时间设置指令

#### 指令格式
```
RTC Config
```

#### 功能描述
设置系统基准时间，支持多种时间格式输入。

#### 操作流程
1. 输入"RTC Config"指令
2. 系统提示"Input Datetime"
3. 输入时间（支持多种格式）
4. 系统返回设置结果

#### 支持的时间格式
- 标准格式：`2025-01-01 12:00:30`
- 中文格式：`2025年01月01日12:00:30`
- 混合格式：`2025-01-01 01-30-10`

#### 成功输出样例
```
RTC Config success
Time:2025-01-01 12:00:30
```

### 2.2 时间查询指令

#### 指令格式
```
RTC now
```

#### 功能描述
显示当前RTC时间。

#### 输出样例
```
Current Time: 2025-01-01 12:00:30
```

## 3. 代码修改说明

### 3.1 修改的文件

1. **APP/system_check.c**
   - 修改`print_system_info()`函数
   - 调整输出格式，使用点号对齐
   - 修正TF卡容量显示格式
   - 调整RTC时间显示格式

2. **APP/usart_app.c**
   - 修改`handle_interactive_input()`中的RTC处理部分
   - 修改"RTC now"命令处理
   - 确保输出格式符合要求

### 3.2 关键函数

- `system_self_check()`: 系统自检主函数
- `check_flash_status()`: Flash状态检测
- `check_tf_card_status()`: TF卡状态检测
- `rtc_set_time_from_string()`: 从字符串设置RTC时间
- `rtc_print_current_time()`: 打印当前RTC时间

## 4. 设计特点

### 4.1 低耦合设计
- 各模块功能独立，接口清晰
- 系统自检模块与其他模块松耦合
- RTC模块独立，便于扩展

### 4.2 中文友好
- 支持中文时间格式输入
- 注释使用中文，便于理解
- 错误提示友好

### 4.3 性能优化
- 最少代码行数实现功能
- 避免重复代码
- 高效的字符串处理

### 4.4 统一配置管理
- 所有变量通过mydefine.h统一管理
- 避免重复定义
- 便于维护

## 5. 测试建议

### 5.1 系统自检测试
1. 确保Flash和TF卡正常连接
2. 输入"test"指令
3. 验证输出格式是否符合要求
4. 测试TF卡拔出情况下的错误处理

### 5.2 RTC功能测试
1. 测试"RTC Config"指令设置时间
2. 测试不同时间格式的输入
3. 测试"RTC now"指令查询时间
4. 验证时间设置的准确性

## 6. 注意事项

1. 确保串口通信正常
2. Flash和TF卡硬件连接正确
3. RTC时钟源配置正确
4. 系统初始化顺序正确

## 7. 扩展性

本实现具有良好的扩展性：
- 可以轻松添加新的检测项目
- 支持更多时间格式
- 可以添加更多系统状态信息
- 便于集成到更大的系统中
