/**
 * @file    system_config.h
 * @brief   系统统一配置管理头文件
 * @details 提供系统级配置、全局变量声明和模块间接口定义
 * <AUTHOR>
 * @date    2024
 */

#ifndef __SYSTEM_CONFIG_H__
#define __SYSTEM_CONFIG_H__

/* ================================ 标准库头文件 ================================ */
#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "stdint.h"
#include "stdlib.h"
#include "math.h"

/* ================================ HAL库头文件 ================================ */
#include "main.h"
#include "usart.h"
#include "adc.h"
#include "tim.h"
#include "dac.h"
#include "i2c.h"

/* ================================ 第三方库头文件 ================================ */
#include "WouoUI.h"
#include "WouoUI_user.h"
#include "u8g2.h"
#include "oled.h"
#include "lfs.h"
#include "lfs_port.h"
#include "gd25qxx.h"
#include "ringbuffer.h"
#include "arm_math.h"
#include "ff.h"    // FATFS文件系统
#include "fatfs.h" // FATFS应用层

/* ================================ 应用层模块头文件 ================================ */
#include "scheduler.h"
#include "oled_app.h"
#include "adc_app.h"
#include "led_app.h"
#include "btn_app.h"
#include "flash_app.h"
#include "usart_app.h"
#include "rtc_app.h"
#include "system_check.h"
#include "config_manager.h"
#include "ini_parser.h"
#include "sampling_control.h"

/* ================================ 系统配置常量 ================================ */
#define SYS_UART_BUFFER_SIZE    128    // 串口缓冲区大小
#define SYS_RINGBUFFER_SIZE     128    // 环形缓冲区大小
#define SYS_SD_PATH_SIZE        4      // SD卡路径长度

/* ================================ 全局变量声明 ================================ */
// 串口通信相关
extern uint16_t g_uart_rx_index;
extern uint32_t g_uart_rx_ticks;
extern uint8_t g_uart_rx_buffer[SYS_UART_BUFFER_SIZE];
extern uint8_t g_uart_rx_dma_buffer[SYS_UART_BUFFER_SIZE];
extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;
extern struct rt_ringbuffer g_uart_ringbuffer;
extern uint8_t g_ringbuffer_pool[SYS_RINGBUFFER_SIZE];
extern uint8_t g_uart_send_flag;
extern uint8_t g_wave_analysis_flag;

// 显示相关
extern u8g2_t g_u8g2_display;

// 存储相关
extern struct lfs_config g_lfs_config;
extern lfs_t g_lfs_instance;
extern RTC_HandleTypeDef hrtc;

// FATFS文件系统相关
extern uint8_t g_sd_return_code;
extern char g_sd_path[SYS_SD_PATH_SIZE];
extern FATFS g_sd_fatfs;
extern FIL g_sd_file;

#endif /* __SYSTEM_CONFIG_H__ */