--cpu=Cortex-M4.fp.sp
"gd32_xifeng\startup_stm32f429xx.o"
"gd32_xifeng\main.o"
"gd32_xifeng\gpio.o"
"gd32_xifeng\adc.o"
"gd32_xifeng\dac.o"
"gd32_xifeng\dma.o"
"gd32_xifeng\i2c.o"
"gd32_xifeng\rtc.o"
"gd32_xifeng\sdio.o"
"gd32_xifeng\spi.o"
"gd32_xifeng\tim.o"
"gd32_xifeng\usart.o"
"gd32_xifeng\stm32f4xx_it.o"
"gd32_xifeng\stm32f4xx_hal_msp.o"
"gd32_xifeng\bsp_driver_sd.o"
"gd32_xifeng\sd_diskio.o"
"gd32_xifeng\fatfs.o"
"gd32_xifeng\diskio.o"
"gd32_xifeng\ff.o"
"gd32_xifeng\ff_gen_drv.o"
"gd32_xifeng\syscall.o"
"gd32_xifeng\cc936.o"
"gd32_xifeng\stm32f4xx_hal_adc.o"
"gd32_xifeng\stm32f4xx_hal_adc_ex.o"
"gd32_xifeng\stm32f4xx_ll_adc.o"
"gd32_xifeng\stm32f4xx_hal_rcc.o"
"gd32_xifeng\stm32f4xx_hal_rcc_ex.o"
"gd32_xifeng\stm32f4xx_hal_flash.o"
"gd32_xifeng\stm32f4xx_hal_flash_ex.o"
"gd32_xifeng\stm32f4xx_hal_flash_ramfunc.o"
"gd32_xifeng\stm32f4xx_hal_gpio.o"
"gd32_xifeng\stm32f4xx_hal_dma_ex.o"
"gd32_xifeng\stm32f4xx_hal_dma.o"
"gd32_xifeng\stm32f4xx_hal_pwr.o"
"gd32_xifeng\stm32f4xx_hal_pwr_ex.o"
"gd32_xifeng\stm32f4xx_hal_cortex.o"
"gd32_xifeng\stm32f4xx_hal.o"
"gd32_xifeng\stm32f4xx_hal_exti.o"
"gd32_xifeng\stm32f4xx_hal_dac.o"
"gd32_xifeng\stm32f4xx_hal_dac_ex.o"
"gd32_xifeng\stm32f4xx_hal_i2c.o"
"gd32_xifeng\stm32f4xx_hal_i2c_ex.o"
"gd32_xifeng\stm32f4xx_hal_rtc.o"
"gd32_xifeng\stm32f4xx_hal_rtc_ex.o"
"gd32_xifeng\stm32f4xx_ll_sdmmc.o"
"gd32_xifeng\stm32f4xx_hal_sd.o"
"gd32_xifeng\stm32f4xx_hal_mmc.o"
"gd32_xifeng\stm32f4xx_hal_spi.o"
"gd32_xifeng\stm32f4xx_hal_tim.o"
"gd32_xifeng\stm32f4xx_hal_tim_ex.o"
"gd32_xifeng\stm32f4xx_hal_uart.o"
"gd32_xifeng\system_stm32f4xx.o"
"gd32_xifeng\ebtn.o"
"gd32_xifeng\oled.o"
"gd32_xifeng\mui.o"
"gd32_xifeng\mui_u8g2.o"
"gd32_xifeng\u8g2_arc.o"
"gd32_xifeng\u8g2_bitmap.o"
"gd32_xifeng\u8g2_box.o"
"gd32_xifeng\u8g2_buffer.o"
"gd32_xifeng\u8g2_button.o"
"gd32_xifeng\u8g2_circle.o"
"gd32_xifeng\u8g2_cleardisplay.o"
"gd32_xifeng\u8g2_d_memory.o"
"gd32_xifeng\u8g2_d_setup.o"
"gd32_xifeng\u8g2_font.o"
"gd32_xifeng\u8g2_fonts.o"
"gd32_xifeng\u8g2_hvline.o"
"gd32_xifeng\u8g2_input_value.o"
"gd32_xifeng\u8g2_intersection.o"
"gd32_xifeng\u8g2_kerning.o"
"gd32_xifeng\u8g2_line.o"
"gd32_xifeng\u8g2_ll_hvline.o"
"gd32_xifeng\u8g2_message.o"
"gd32_xifeng\u8g2_polygon.o"
"gd32_xifeng\u8g2_selection_list.o"
"gd32_xifeng\u8g2_setup.o"
"gd32_xifeng\u8log.o"
"gd32_xifeng\u8log_u8g2.o"
"gd32_xifeng\u8log_u8x8.o"
"gd32_xifeng\u8x8_8x8.o"
"gd32_xifeng\u8x8_byte.o"
"gd32_xifeng\u8x8_cad.o"
"gd32_xifeng\u8x8_capture.o"
"gd32_xifeng\u8x8_d_ssd1306_128x32.o"
"gd32_xifeng\u8x8_debounce.o"
"gd32_xifeng\u8x8_display.o"
"gd32_xifeng\u8x8_fonts.o"
"gd32_xifeng\u8x8_gpio.o"
"gd32_xifeng\u8x8_input_value.o"
"gd32_xifeng\u8x8_message.o"
"gd32_xifeng\u8x8_selection_list.o"
"gd32_xifeng\u8x8_setup.o"
"gd32_xifeng\u8x8_string.o"
"gd32_xifeng\u8x8_u8toa.o"
"gd32_xifeng\u8x8_u16toa.o"
"gd32_xifeng\ringbuffer.o"
"gd32_xifeng\gd25qxx.o"
"gd32_xifeng\lfs.o"
"gd32_xifeng\lfs_port.o"
"gd32_xifeng\lfs_util.o"
"gd32_xifeng\wououi.o"
"gd32_xifeng\wououi_anim.o"
"gd32_xifeng\wououi_font.o"
"gd32_xifeng\wououi_graph.o"
"gd32_xifeng\wououi_msg.o"
"gd32_xifeng\wououi_page.o"
"gd32_xifeng\wououi_user.o"
"gd32_xifeng\wououi_win.o"
"gd32_xifeng\scheduler.o"
"gd32_xifeng\usart_app.o"
"gd32_xifeng\led_app.o"
"gd32_xifeng\btn_app.o"
"gd32_xifeng\adc_app.o"
"gd32_xifeng\oled_app.o"
"gd32_xifeng\flash_app.o"
"gd32_xifeng\rtc_app.o"
"gd32_xifeng\system_check.o"
"gd32_xifeng\config_manager.o"
"gd32_xifeng\ini_parser.o"
"gd32_xifeng\sampling_control.o"
"gd32_xifeng\data_storage.o"
"gd32_xifeng\device_id.o"
"..\Middlewares\ST\ARM\DSP\Lib\arm_cortexM4l_math.lib"
--library_type=microlib --strict --scatter "GD32_Xifeng\GD32_Xifeng.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "GD32_Xifeng.map" -o GD32_Xifeng\GD32_Xifeng.axf