/**
 * @file    usart_app.h
 * @brief   串口通信应用模块头文件
 * @details 提供串口命令解析、数据传输和交互式命令处理接口
 * <AUTHOR>
 * @date    2024
 * @version 2.0
 */

#ifndef __USART_APP_H__
#define __USART_APP_H__

#include "system_config.h"  // 系统配置头文件
#include "data_storage.h"   // 数据存储模块

/* ================================ 类型定义 ================================ */
/**
 * @brief 串口操作状态枚举
 */
typedef enum {
    UART_STATUS_OK = 0,      // 操作成功
    UART_STATUS_ERROR,       // 操作失败
    UART_STATUS_BUSY,        // 串口忙碌
    UART_STATUS_TIMEOUT      // 操作超时
} uart_status_t;

/**
 * @brief 命令状态枚举
 */
typedef enum {
    CMD_STATE_IDLE = 0,       // 空闲状态
    CMD_STATE_WAIT_RATIO,     // 等待ratio参数输入
    CMD_STATE_WAIT_LIMIT,     // 等待limit参数输入
    CMD_STATE_WAIT_RTC        // 等待RTC时间输入
} cmd_state_t;

/**
 * @brief 输出格式枚举
 */
typedef enum {
    OUTPUT_FORMAT_NORMAL = 0, // 正常输出格式
    OUTPUT_FORMAT_HIDDEN      // 隐藏输出格式
} output_format_t;

/* ================================ 公共接口函数 ================================ */
/**
 * @brief 格式化串口输出函数
 * @param huart 串口句柄
 * @param format 格式字符串
 * @param ... 可变参数
 * @return int 输出字符数
 */
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);

/**
 * @brief 串口任务处理函数
 * @note 在任务调度器中周期性调用
 */
void uart_task(void);

/**
 * @brief 解析并执行串口命令
 * @param buffer 命令缓冲区
 * @param length 命令长度
 * @return uart_status_t 执行状态
 */
uart_status_t parse_uart_command(uint8_t *buffer, uint16_t length);

/* ================================ 命令处理函数 ================================ */
void handle_conf_command(void);             // 处理conf命令
void handle_ratio_command(void);            // 处理ratio命令
void handle_limit_command(void);            // 处理limit命令
void handle_configsave_command(void);       // 处理configsave命令
void handle_configread_command(void);       // 处理configread命令
void handle_start_command(void);            // 处理start命令
void handle_stop_command(void);             // 处理stop命令
void handle_hide_command(void);             // 处理hide命令
void handle_unhide_command(void);           // 处理unhide命令
void handle_rtc_config_command(void);       // 处理RTC Config命令
void handle_sampling_output(void);          // 处理采样数据输出
uart_status_t handle_interactive_input(char *input); // 处理交互式输入

/* ================================ 工具函数 ================================ */
/**
 * @brief 将RTC时间转换为Unix时间戳
 * @param time RTC时间结构体
 * @param date RTC日期结构体
 * @return uint32_t Unix时间戳
 */
uint32_t convert_rtc_to_unix_timestamp(RTC_TimeTypeDef *time, RTC_DateTypeDef *date);

/**
 * @brief 格式化HEX输出
 * @param timestamp 时间戳
 * @param voltage 电压值
 * @param is_overlimit 超限标志
 * @param output 输出缓冲区
 */
void format_hex_output(uint32_t timestamp, float voltage, uint8_t is_overlimit, char *output);

/* ================================ 全局变量声明 ================================ */
extern uint8_t g_sampling_output_enabled;  // 采样输出使能标志
extern uint32_t g_last_output_time;        // 上次输出时间
extern output_format_t g_output_format;    // 当前输出格式

#endif /* __USART_APP_H__ */
