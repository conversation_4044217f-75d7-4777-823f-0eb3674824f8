#include "system_check.h"
#include "gd25qxx.h"     // Flash�����ӿ�
#include "ff.h"          // FATFS�ļ�ϵͳ
#include "fatfs.h"       // FATFS����
#include "rtc_app.h"     // RTCӦ�ýӿ�
#include "diskio.h"      // ����IO�ӿ�
#include "data_storage.h" // ���ݴ洢ģ��

// Copyright (c) 2024 �״׵��ӹ�����. All rights reserved.

// Flash�ͺ�ʶ��� - ����չ���
typedef struct
{
    uint32_t id;
    const char *model;
    uint32_t capacity_mb;
} flash_model_t;

static const flash_model_t flash_models[] = {
    {0xC84017, "GD25Q64", 8}, // 8MB
    {0xC84016, "GD25Q32", 4}, // 4MB
    {0xC84015, "GD25Q16", 2}, // 2MB
    {0xC84014, "GD25Q80", 1}, // 1MB
    {0xC84013, "GD25Q40", 1}, // 512KB = 0.5MB����ʾΪ1MB
    {0xEF4017, "W25Q64", 8},  // Winbond 8MB
    {0xEF4016, "W25Q32", 4},  // Winbond 4MB
    {0xEF4015, "W25Q16", 2},  // Winbond 2MB
    {0x000000, "Unknown", 0}  // δ֪�ͺ�
};

// ����Flash ID��ȡ�ͺ���Ϣ
const char *get_flash_model_name(uint32_t flash_id)
{
    for (int i = 0; flash_models[i].id != 0x000000; i++)
    {
        if (flash_models[i].id == flash_id)
        {
            return flash_models[i].model;
        }
    }
    return "Unknown";
}

// ��ȡFlash����
static uint32_t get_flash_capacity(uint32_t flash_id)
{
    for (int i = 0; flash_models[i].id != 0x000000; i++)
    {
        if (flash_models[i].id == flash_id)
        {
            return flash_models[i].capacity_mb;
        }
    }
    return 0;
}

// ��ȡSD������(KB)
static uint32_t sd_card_capacity_get(void)
{
    DWORD sector_count = 0;
    WORD sector_size = 0;

    // ��ȡ��������
    if (disk_ioctl(0, GET_SECTOR_COUNT, &sector_count) == RES_OK)
    {
        // ��ȡ������С
        if (disk_ioctl(0, GET_SECTOR_SIZE, &sector_size) == RES_OK)
        {
            // ��������(KB) = ������ * ������С / 1024
            return (uint32_t)((uint64_t)sector_count * sector_size / 1024);
        }
    }
    return 0;
}

// Flash״̬���
system_check_status_t check_flash_status(flash_info_t *flash_info)
{
    if (flash_info == NULL)
    {
        return SYSTEM_CHECK_ERROR;
    }

    // ��ȡFlash ID
    flash_info->flash_id = spi_flash_read_id();

    // ����Ƿ�Ϊ��ЧID
    if (flash_info->flash_id == 0x000000 || flash_info->flash_id == 0xFFFFFF)
    {
        flash_info->status = SYSTEM_CHECK_NOT_FOUND;
        strcpy(flash_info->model_name, "Not Found");
        flash_info->capacity_mb = 0;
        return SYSTEM_CHECK_NOT_FOUND;
    }

    // ��ȡ�ͺź�������Ϣ
    const char *model = get_flash_model_name(flash_info->flash_id);
    strcpy(flash_info->model_name, model);
    flash_info->capacity_mb = get_flash_capacity(flash_info->flash_id);
    flash_info->status = SYSTEM_CHECK_OK;

    return SYSTEM_CHECK_OK;
}

// SD��״̬���
system_check_status_t check_tf_card_status(sd_card_info_t *sd_info)
{
    if (sd_info == NULL)
    {
        return SYSTEM_CHECK_ERROR;
    }

    // ʹ��disk_initialize���SD��
    DSTATUS sd_status = disk_initialize(0);
    if (sd_status == 0)
    {
        // SD����ʼ���ɹ�����ȡ������Ϣ
        uint32_t capacity_kb = sd_card_capacity_get();

        sd_info->capacity_mb = capacity_kb / 1024; // ת��ΪMB(����ʾ��Ҫ)
        sd_info->sector_size = 512;                // ��׼������С
        sd_info->sector_count = capacity_kb * 2;   // KBת��Ϊ������(512�ֽ�/����)
        sd_info->status = SYSTEM_CHECK_OK;

        return SYSTEM_CHECK_OK;
    }
    else
    {
        // SD�����ʧ��
        sd_info->status = SYSTEM_CHECK_NOT_FOUND;
        sd_info->capacity_mb = 0;
        sd_info->sector_count = 0;
        sd_info->sector_size = 0;

        return SYSTEM_CHECK_NOT_FOUND;
    }
}

// ��ӡRTCʱ��
void print_rtc_time(void)
{
    rtc_print_current_time(); // ����RTCӦ��ģ���ʱ���ӡ����
}

// ��ӡϵͳ��Ϣ - �������ʽ���
void print_system_info(const system_info_t *info)
{
    my_printf(&huart1, "======system selftest======\r\n");

    // Flash�����
    if (info->flash_info.status == SYSTEM_CHECK_OK)
    {
        my_printf(&huart1, "flash.............ok\r\n");
    }
    else
    {
        my_printf(&huart1, "flash.............error\r\n");
    }

    // SD�������
    if (info->sd_info.status == SYSTEM_CHECK_OK)
    {
        my_printf(&huart1, "TF card..............ok\r\n");
    }
    else
    {
        my_printf(&huart1, "TF card..............error\r\n");
    }

    // ��ʾFlash ID
    my_printf(&huart1, "flash ID��0x%06X\r\n", info->flash_info.flash_id);

    // ��ʾTF������(����ɹ�ʱ)����ʾ������Ϣ(ʧ��ʱ)
    if (info->sd_info.status == SYSTEM_CHECK_OK)
    {
        // ����KB����������ʾ
        uint32_t capacity_kb = info->sd_info.capacity_mb * 1024;
        my_printf(&huart1, "TF card memory: %lu KB\r\n", capacity_kb);
    }
    else
    {
        my_printf(&huart1, "can not find TF card\r\n");
    }

    // RTCʱ��
    my_printf(&huart1, "RTC: ");
    print_rtc_time();

    my_printf(&huart1, "======system selftest======\r\n");
}

// ϵͳ�Լ�������
void system_self_check(void)
{
    system_info_t system_info = {0}; // ��ʼ��ϵͳ��Ϣ�ṹ��

    // ��¼ϵͳ�Լ���ʼ��־
    data_storage_write_log("system hardware test");

    // ִ�и�����
    check_flash_status(&system_info.flash_info);
    check_tf_card_status(&system_info.sd_info);
    system_info.rtc_status = SYSTEM_CHECK_OK; // RTC״̬Ĭ��OK

    // ��¼�������־
    if (system_info.flash_info.status == SYSTEM_CHECK_OK)
    {
        data_storage_write_log("test ok - flash");
    }
    else
    {
        data_storage_write_log("test error: flash not found");
    }

    if (system_info.sd_info.status == SYSTEM_CHECK_OK)
    {
        data_storage_write_log("test ok - tf card");
    }
    else
    {
        data_storage_write_log("test error: tf card not found");
    }

    // ��ӡ�����
    print_system_info(&system_info);
}
