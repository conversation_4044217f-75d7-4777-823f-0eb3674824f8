gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/MatrixFunctions.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_add_f32.c
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/matrix_functions.h
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\arm_math_types.h
gd32_xifeng\matrixfunctions.o: ../Drivers/CMSIS/Include/cmsis_compiler.h
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
gd32_xifeng\matrixfunctions.o: ../Drivers/CMSIS/Include/cmsis_armcc.h
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\float.h
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\ARMCC\Bin\..\include\limits.h
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\arm_math_memory.h
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/none.h
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/utils.h
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_add_q15.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_add_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cmplx_mult_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cmplx_mult_q15.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cmplx_mult_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_init_f64.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_init_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_init_q15.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_init_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_inverse_f32.c
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/matrix_utils.h
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_inverse_f64.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_mult_f64.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_mult_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_mult_fast_q15.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_mult_fast_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_mult_q7.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_mult_q15.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_mult_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_mult_opt_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_scale_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_scale_q15.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_scale_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_sub_f64.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_sub_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_sub_q15.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_sub_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_trans_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_trans_f64.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_trans_q7.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_trans_q15.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_trans_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_vec_mult_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_vec_mult_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_vec_mult_q15.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_vec_mult_q7.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cmplx_trans_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cmplx_trans_q31.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cmplx_trans_q15.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cholesky_f64.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_cholesky_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_solve_upper_triangular_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_solve_lower_triangular_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_solve_upper_triangular_f64.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_solve_lower_triangular_f64.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_ldlt_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_ldlt_f64.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_qr_f32.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_mat_qr_f64.c
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_householder_f64.c
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/basic_math_functions.h
gd32_xifeng\matrixfunctions.o: D:\Keil_v5\ARM\Packs\ARM\CMSIS-DSP\1.16.2\Include\dsp/fast_math_functions.h
gd32_xifeng\matrixfunctions.o: D:/Keil_v5/ARM/Packs/ARM/CMSIS-DSP/1.16.2/Source/MatrixFunctions/arm_householder_f32.c
